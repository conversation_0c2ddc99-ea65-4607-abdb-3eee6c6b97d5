import {IconAiTools1, IconAiTools2, IconAiTools3, IconAiTools4} from '@/icons/mcp';
import {FeatureData, StepData} from './types';

export const features: FeatureData[] = [
    {
        title: '降低双边成本',
        content: '为供需双方降本，实现开发、接入、调用成本优化',
        icon: IconAiTools1,
    },
    {
        title: '协议无缝转换',
        content: '统一转换层，支持MCP、OpenAPI、脚本快速转换',
        icon: IconAiTools2,
    },
    {
        title: '工具精准调用',
        content: '提供多维调试入口，帮助用户发现、高效调用工具',
        icon: IconAiTools3,
    },
    {
        title: '构建完整生态',
        content: '打造服务到编排的闭环，赋能开发者与业务方创新',
        icon: IconAiTools4,
    },
];

export const producerSteps: StepData[] = [
    {
        title: '便捷注册',
        description: '快速将MCP或OpenAPI封装为MCP Server，完成接入',
    },
    {
        title: '灵活配置',
        description: '自定义工具参数与模板，确保模型能高效调用',
    },
    {
        title: '在线调试',
        description: '配置后可在线调试验证，查看返回结果，确保可靠',
    },
    {
        title: '发布共享',
        description: '将MCP Server发布到广场，分享生态提升服务价值',
    },
];

export const consumerSteps: StepData[] = [
    {
        title: '广场发现',
        description: '在广场多维度探索筛选MCP，精准匹配业务需求',
    },
    {
        title: '组合试用',
        description: '在Playground组合试用，与大模型交互评估效果',
    },
    {
        title: '一键订阅',
        description: '按需创建应用并订阅MCP，灵活按需配置工具',
    },
    {
        title: '集成调用',
        description: '将已订阅工具轻松集成至AI Agent，即刻扩展能力',
    },
];
