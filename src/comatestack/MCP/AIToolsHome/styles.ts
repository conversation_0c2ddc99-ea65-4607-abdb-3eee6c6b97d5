/* eslint-disable max-lines */
/* eslint-disable max-len */
import styled from '@emotion/styled';
import {Flex} from 'antd';
import mcpAIToolsBg from '@/assets/mcp/mcpAIToolsBg.png';

export const Container = styled.div`
    width: 100%;
    min-height: calc(100vh - 48px);
    position: relative;
    background-image: url(${mcpAIToolsBg});
    background-repeat: no-repeat;
    background-position: top center;
    overflow: hidden;
`;

export const TopNavArea = styled(Flex)`
    position: absolute;
    top: 20px;
    right: 56px;
`;

export const NavLink = styled.a`
    font-size: 14px;
    line-height: 22px;
    color: #181818;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    padding: 5px 16px;

    svg {
        margin-left: 8px;
        color: #0080FF;
    }
`;

export const MainTitleContainer = styled.div`
    position: absolute;
    margin-top: 64px;
    margin-left: 56px;
`;

export const Description = styled.div`
    font-size: 14px;
    line-height: 22px;
    color: #8D8D8D;
    margin: 16px 0 0 0;
`;

export const ButtonArea = styled(Flex)`
    margin-top: 40px;
    gap: 16px;
`;

export const FeaturesContainer = styled(Flex)`
    margin-top: 359px;
    margin-left: 56px;
    margin-right: 56px;
    justify-content: space-between;
`;

export const FeatureCard = styled.div`
    flex: 1;
    height: 144px;
    border: 2px solid #FFFFFF;
    border-radius: 8px;
    padding: 24px 16px;
    background: #fff;
    margin-right: 16px;
    position: relative;
    background: linear-gradient(
        178.56deg,
        rgba(204, 229, 255, 0.8) -44.84%,
        rgba(230, 242, 255, 0.8) -5.04%,
        rgba(242, 249, 255, 0.8) 24.57%,
        rgba(255, 255, 255, 0.8) 56.11%
    );
    backdrop-filter: blur(40px);
    box-shadow: 0px 0px 8px 0px #1B1B1B1A;

    &:last-child {
        margin-right: 0;
    }

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
`;

export const FeatureTitle = styled.div`
    font-weight: 500;
    font-size: 20px;
    line-height: 36px;
    color: #181818;
    margin-bottom: 8px;
`;

export const FeatureContent = styled.div`

    font-size: 14px;
    line-height: 20px;
    color: #8F8F8F;
`;

export const IconWrapper = styled.div`
    position: absolute;
    right: 16px;
    top: -16px;
`;
