import styled from '@emotion/styled';
import {Flex} from 'antd';
import {Button} from '@panda-design/components';

export const ProcessContainer = styled(Flex)`
    margin: 40px 56px 0 56px;
    gap: 16px;
`;

export const ProcessCard = styled.div`
    flex: 1;
    border-radius: 8px;
    box-shadow: 0px 0px 8px 0px #00000014;
`;

export const ProcessHeader = styled(Flex)`
    width: 100%;
    height: 66px;
    background: linear-gradient(88.32deg, #E5F2FF 1.61%, rgba(255, 254, 240, 0.8) 99.57%);
    align-items: center;
    justify-content: space-between;
    border-radius: 8px 8px 0 0;
    position: relative;
`;

export const ProcessTitle = styled.div`
    font-weight: 500;
    font-size: 20px;
    line-height: 36px;
    font-style: Medium;
    margin-left: 62px;
`;

export const ProcessSteps = styled.div`
    padding: 41px 24px 41px 64px;
`;

export const ProcessStep = styled.div`
    display: flex;
    margin-bottom: 20px;
    position: relative;

    &:last-child {
        margin-bottom: 0;
    }

    &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 7px;
        top: 24px;
        width: 0.5px;
        height: calc(100% - 8px);
        border-left: 0.5px dashed #BFBFBF;
        border-left-style: dashed;
        border-left-width: 0.5px;
    }
`;

export const StepIndicator = styled.div`
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #D9D9D9;
    margin-right: 16px;
    margin-top: 2px;
    flex-shrink: 0;
`;

export const StepContent = styled.div`
    flex: 1;
`;

export const StepTitle = styled.div`
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #0080FF;
    margin: 0 0 8px 0;
`;

export const StepDescription = styled.p`
    color: #8F8F8F;
    line-height: 22px;
`;

export const ProcessDetailButton = styled(Button)`
    position: absolute;
    top: 50%;
    right: 40px;
    transform: translateY(-50%);
    padding: 5px 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;

    line-height: 22px;
    color: #0080FF;

    svg {
        color: #0080FF;
    }
`;
